# ═══════════════════════════════════════════════════════════════════════════════
# 🧬 STRATEGY EVOLUTION AGENT CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════

# Evolution Algorithm Configuration
evolution:
  # Population parameters
  population_size: 50                    # Number of strategies in population
  elite_size: 10                        # Number of elite strategies to preserve
  max_generations: 100                   # Maximum number of generations

  # DYNAMIC STRATEGY SELECTION (NEW)
  strategy_selection:
    mode: "diverse_selection"            # base_only, top_performers, diverse_selection, ranking_based
    min_ranking: 0                       # Minimum ranking for ranking_based mode (0-100)

  # Genetic algorithm parameters
  mutation_rate: 0.1                     # Probability of mutation (10%)
  crossover_rate: 0.8                    # Probability of crossover (80%)
  tournament_size: 5                     # Tournament selection size
  convergence_threshold: 0.001           # Fitness variance threshold for convergence

  # Strategy management
  max_strategies_per_symbol: 5           # Maximum strategies per symbol
  min_performance_threshold: 0.05        # Minimum performance to keep strategy (5%)
  performance_evaluation_period: 30      # Days to evaluate performance

  # Market regime adaptation
  regime_adaptation_enabled: true        # Enable market regime adaptation
  regime_specific_evolution: true        # Evolve strategies for specific regimes

  # Risk management
  max_risk_per_strategy: 0.02           # Maximum risk per strategy (2%)
  portfolio_risk_limit: 0.10            # Total portfolio risk limit (10%)

  # 🔧 HYPERPARAMETER OPTIMIZATION (FREE ALTERNATIVES)
  enable_hyperparameter_optimization: true    # Enable free GPU-accelerated optimization
  optimization_method: "auto"                 # auto, skopt_gp, hyperopt_tpe, ray_tune_asha, random_search
  optimization_trials: 300                     # Number of optimization trials (reduced for speed)
  optimization_timeout: 1800                  # Optimization timeout in seconds (30 minutes)
  parallel_jobs: 4                           # Number of parallel optimization jobs

  # ⚖️ RISK-REWARD OPTIMIZATION
  rr_combinations:                            # Risk-reward ratios to test
    - [1.0, 1.0]    # 1:1
    - [1.0, 1.5]    # 1:1.5
    - [1.0, 2.0]    # 1:2
    - [1.0, 2.5]    # 1:2.5
    - [1.0, 3.0]    # 1:3
    - [1.5, 2.0]    # 1.5:2
    - [1.5, 3.0]    # 1.5:3
    - [2.0, 3.0]    # 2:3

  # 🕐 TIME-AWARE OPTIMIZATION
  enable_time_aware_optimization: true        # Enable time window optimization
  time_windows:                               # Trading time windows
    - "morning"     # 9:15-11:00
    - "midday"      # 11:00-13:30
    - "afternoon"   # 13:30-15:25

  # 🧬 RULE MUTATION SETTINGS
  rule_mutation_rate: 0.15                    # Probability of rule mutation
  condition_simplification_rate: 0.05         # Probability of condition simplification
  mutation_types:                             # Types of mutations to apply
    - "parameter_tweak"
    - "condition_modify"
    - "indicator_replace"
    - "logic_simplify"
    - "new_feature_inject"

  # 🧠 PERFORMANCE-DRIVEN SELECTION
  enable_symbol_specific_ranking: true        # Enable symbol-specific strategy ranking
  prune_underperformers: true                 # Remove poor performing strategies
  underperformer_roi_threshold: 0.03          # 3% ROI threshold for pruning
  underperformer_accuracy_threshold: 0.45     # 45% accuracy threshold for pruning

  # 🤖 AUTONOMOUS STRATEGY DISCOVERY
  enable_autonomous_discovery: true           # Enable autonomous strategy discovery
  symbolic_regression_enabled: false         # Enable symbolic regression (requires gplearn)
  pattern_discovery_enabled: true            # Enable pattern-based discovery

# Performance Evaluation Configuration
performance:
  # Evaluation parameters
  evaluation_period_days: 30             # Performance evaluation period
  min_trades_threshold: 10               # Minimum trades for valid evaluation

  # 🎯 MULTI-METRIC TARGETING
  fitness_weights:
    roi: 0.25                           # Return on Investment weight
    sharpe_ratio: 0.20                  # Sharpe ratio weight
    max_drawdown: -0.15                 # Max drawdown weight (negative = penalty)
    profit_factor: 0.15                 # Profit factor weight
    win_rate: 0.10                      # Win rate weight
    expectancy: 0.10                    # Expectancy weight
    calmar_ratio: 0.05                  # Calmar ratio weight

  # Optimization targets for multi-objective optimization
  optimization_targets:
    - metric: "roi"
      weight: 0.3
      direction: "maximize"
      threshold: 0.05
    - metric: "sharpe_ratio"
      weight: 0.25
      direction: "maximize"
      threshold: 1.0
    - metric: "max_drawdown"
      weight: 0.2
      direction: "minimize"
      threshold: 0.15
    - metric: "profit_factor"
      weight: 0.15
      direction: "maximize"
      threshold: 1.2
    - metric: "win_rate"
      weight: 0.1
      direction: "maximize"
      threshold: 0.5

  # Performance thresholds
  excellent_performance_threshold: 0.8   # Threshold for excellent performance
  good_performance_threshold: 0.5        # Threshold for good performance
  poor_performance_threshold: 0.2        # Threshold for poor performance

  # 📊 THRESHOLD TUNING RANGES
  parameter_ranges:
    rsi_window: [5, 50]                  # RSI period range
    rsi_oversold: [10, 40]               # RSI oversold level range
    rsi_overbought: [60, 90]             # RSI overbought level range
    macd_fast: [8, 21]                   # MACD fast EMA range
    macd_slow: [21, 50]                  # MACD slow EMA range
    ema_short: [3, 15]                   # Short EMA range
    ema_long: [15, 100]                  # Long EMA range
    volume_multiplier: [1.0, 4.0]        # Volume multiplier range
    stop_loss_pct: [0.005, 0.05]        # Stop loss percentage range
    take_profit_pct: [0.01, 0.10]        # Take profit percentage range
    position_size_pct: [0.01, 0.15]      # Position size percentage range

# 📈 TECHNICAL ANALYSIS CONFIGURATION
technical_analysis:
  # Available indicators for strategy generation
  available_indicators:
    - "RSI_5"
    - "RSI_14"
    - "RSI_21"
    - "EMA_5"
    - "EMA_8"
    - "EMA_13"
    - "EMA_20"
    - "EMA_21"
    - "EMA_30"
    - "EMA_50"
    - "EMA_100"
    - "SMA_20"
    - "SMA_50"
    - "MACD"
    - "MACD_signal"
    - "STOCH_K"
    - "STOCH_D"
    - "CCI"
    - "ADX"
    - "MFI"
    - "BB_upper"
    - "BB_lower"
    - "BB_width"
    - "ATR"
    - "VWAP"
    - "SuperTrend"
    - "Donchian_high"
    - "Donchian_low"
    - "Pivot_Point"
    - "Support_1"
    - "Resistance_1"
    - "Volume"
    - "Volume_SMA"
    - "OBV"

  # 🧱 NEW INDICATOR INJECTION
  new_indicators:
    - "Williams_R"
    - "ROC"
    - "TSI"
    - "Aroon_up"
    - "Aroon_down"
    - "Chaikin_MF"
    - "Commodity_Channel_Index"
    - "Detrended_Price_Oscillator"
    - "Ease_of_Movement"
    - "Force_Index"
    - "Mass_Index"
    - "Negative_Volume_Index"
    - "On_Balance_Volume"
    - "Price_Volume_Trend"
    - "Positive_Volume_Index"
    - "Volume_Rate_of_Change"

# 📋 STRATEGY TEMPLATES
strategy_templates:
  # Mean reversion template
  mean_reversion:
    name: "Mean_Reversion_Template"
    base_conditions:
      - "RSI_{rsi_period} < {oversold_level}"
      - "Close < EMA_{ema_period} * {deviation_factor}"
      - "Volume > Volume.rolling({volume_period}).mean() * {volume_multiplier}"
    parameters:
      rsi_period: [10, 21]
      oversold_level: [20, 35]
      ema_period: [15, 30]
      deviation_factor: [0.98, 1.02]
      volume_period: [15, 25]
      volume_multiplier: [1.2, 2.5]
    fitness_targets:
      - metric: "sharpe_ratio"
        weight: 0.4
        direction: "maximize"
      - metric: "max_drawdown"
        weight: 0.3
        direction: "minimize"
      - metric: "win_rate"
        weight: 0.3
        direction: "maximize"

  # Momentum template
  momentum:
    name: "Momentum_Template"
    base_conditions:
      - "EMA_{fast_period} > EMA_{slow_period}"
      - "RSI_{rsi_period} > {momentum_threshold}"
      - "Volume > Volume.rolling({volume_period}).mean() * {volume_multiplier}"
      - "ADX > {adx_threshold}"
    parameters:
      fast_period: [5, 15]
      slow_period: [20, 50]
      rsi_period: [10, 21]
      momentum_threshold: [50, 70]
      volume_period: [15, 25]
      volume_multiplier: [1.5, 3.0]
      adx_threshold: [20, 40]
    fitness_targets:
      - metric: "roi"
        weight: 0.4
        direction: "maximize"
      - metric: "profit_factor"
        weight: 0.3
        direction: "maximize"
      - metric: "expectancy"
        weight: 0.3
        direction: "maximize"

  # Breakout template
  breakout:
    name: "Breakout_Template"
    base_conditions:
      - "Close > BB_upper"
      - "Volume > Volume.rolling({volume_period}).mean() * {volume_multiplier}"
      - "ATR > ATR.rolling({atr_period}).mean() * {atr_multiplier}"
    parameters:
      volume_period: [15, 25]
      volume_multiplier: [2.0, 4.0]
      atr_period: [10, 20]
      atr_multiplier: [1.2, 2.0]
    fitness_targets:
      - metric: "profit_factor"
        weight: 0.5
        direction: "maximize"
      - metric: "max_drawdown"
        weight: 0.3
        direction: "minimize"
      - metric: "roi"
        weight: 0.2
        direction: "maximize"

# Agent Integration Configuration
agents:
  # Performance Analysis Agent
  performance_analysis_agent:
    enabled: true
    config_path: "config/performance_analysis_config.yaml"
    query_timeout: 30                    # Query timeout in seconds
    
  # Market Monitoring Agent
  market_monitoring_agent:
    enabled: true
    config_path: "config/market_monitoring_config.yaml"
    query_timeout: 30
    
  # AI Training Agent
  ai_training_agent:
    enabled: true
    config_path: "config/ai_training_config.yaml"
    query_timeout: 60                    # Longer timeout for AI operations
    
  # Backtesting Agent (for strategy validation)
  backtesting_agent:
    enabled: true
    config_path: "config/backtesting_config.yaml"
    query_timeout: 300                   # 5 minutes for backtesting

# Storage Configuration
storage:
  # Directory paths
  strategies_dir: "data/evolved_strategies"      # Evolved strategies storage
  performance_dir: "data/evolution_performance"  # Performance data storage
  backup_dir: "data/evolution_backups"          # Backup storage
  
  # File management
  max_backup_files: 10                   # Maximum backup files to keep
  backup_interval_hours: 24              # Backup interval in hours
  cleanup_old_files: true                # Enable automatic cleanup
  max_file_age_days: 90                  # Maximum file age before cleanup

# Timing Configuration
timing:
  # Loop intervals (in seconds)
  evolution_interval: 1800               # Evolution loop interval (30 minutes)
  monitoring_interval: 900               # Performance monitoring interval (15 minutes)
  regime_adaptation_interval: 1800       # Regime adaptation interval (30 minutes)
  management_interval: 3600              # Strategy management interval (1 hour)
  
  # Evolution timing
  min_evolution_interval_minutes: 30     # Minimum time between evolutions
  max_evolution_time_minutes: 60         # Maximum time for single evolution
  
  # Performance evaluation timing
  performance_update_interval: 300       # Performance update interval (5 minutes)
  trend_analysis_interval: 1800          # Trend analysis interval (30 minutes)

# Market Regime Configuration
market_regimes:
  # Regime detection parameters
  bull_market_threshold: 0.05            # 5% upward trend threshold
  bear_market_threshold: -0.05           # 5% downward trend threshold
  high_volatility_threshold: 0.02        # 2% volatility threshold
  
  # Regime-specific adaptations
  bull_market_adaptations:
    take_profit_multiplier: 1.2          # Increase take profit by 20%
    position_size_multiplier: 1.1        # Increase position size by 10%
    
  bear_market_adaptations:
    stop_loss_multiplier: 0.8            # Tighten stop loss by 20%
    position_size_multiplier: 0.8        # Reduce position size by 20%
    
  sideways_market_adaptations:
    rsi_oversold_level: 25               # More extreme RSI levels
    rsi_overbought_level: 75
    
  high_volatility_adaptations:
    stop_loss_multiplier: 1.5            # Wider stops for high volatility
    position_size_multiplier: 0.7        # Smaller positions
    
  low_volatility_adaptations:
    stop_loss_multiplier: 0.7            # Tighter stops for low volatility
    position_size_multiplier: 1.3        # Larger positions

# Strategy Gene Configuration
strategy_genes:
  # RSI parameters
  rsi_period:
    min_value: 5
    max_value: 50
    default_value: 14
    mutation_strength: 0.1
    
  rsi_oversold:
    min_value: 10
    max_value: 40
    default_value: 30
    mutation_strength: 0.1
    
  rsi_overbought:
    min_value: 60
    max_value: 90
    default_value: 70
    mutation_strength: 0.1
  
  # EMA parameters
  ema_fast:
    min_value: 3
    max_value: 15
    default_value: 5
    mutation_strength: 0.1
    
  ema_slow:
    min_value: 15
    max_value: 50
    default_value: 20
    mutation_strength: 0.1
    
  ema_trend:
    min_value: 30
    max_value: 100
    default_value: 50
    mutation_strength: 0.1
  
  # Volume parameters
  volume_multiplier:
    min_value: 1.0
    max_value: 3.0
    default_value: 1.5
    mutation_strength: 0.1
    
  volume_period:
    min_value: 10
    max_value: 50
    default_value: 20
    mutation_strength: 0.1
  
  # Risk management parameters
  stop_loss_pct:
    min_value: 0.005
    max_value: 0.05
    default_value: 0.02
    mutation_strength: 0.1
    
  take_profit_pct:
    min_value: 0.01
    max_value: 0.10
    default_value: 0.04
    mutation_strength: 0.1
    
  position_size_pct:
    min_value: 0.01
    max_value: 0.15
    default_value: 0.05
    mutation_strength: 0.1

# Logging Configuration
logging:
  level: "INFO"                          # Logging level (DEBUG, INFO, WARNING, ERROR)
  file: "logs/strategy_evolution.log"    # Log file path
  max_file_size_mb: 100                  # Maximum log file size
  backup_count: 5                        # Number of backup log files
  
  # Component-specific logging
  evolution_logging: true                # Log evolution details
  performance_logging: true              # Log performance details
  regime_logging: true                   # Log regime adaptation details
  strategy_logging: true                 # Log strategy management details

# Monitoring and Alerts Configuration
monitoring:
  # Health checks
  enable_health_checks: true             # Enable health monitoring
  health_check_interval: 300             # Health check interval (5 minutes)
  
  # Performance alerts
  enable_performance_alerts: true        # Enable performance alerts
  poor_performance_alert_threshold: 0.1  # Alert threshold for poor performance
  excellent_performance_alert_threshold: 0.8  # Alert threshold for excellent performance
  
  # System alerts
  enable_system_alerts: true             # Enable system alerts
  memory_usage_alert_threshold: 0.8      # Memory usage alert threshold (80%)
  disk_usage_alert_threshold: 0.9        # Disk usage alert threshold (90%)

# Development and Testing Configuration
development:
  # Testing parameters
  enable_simulation_mode: false          # Enable simulation mode for testing
  simulation_data_path: "data/test/simulation_data.csv"  # Test data path
  
  # Debug settings
  enable_debug_logging: false            # Enable debug logging
  save_intermediate_results: false       # Save intermediate evolution results
  
  # Performance testing
  enable_performance_profiling: false    # Enable performance profiling
  profiling_output_dir: "data/profiling" # Profiling output directory

# API Configuration (for external integration)
api:
  # REST API settings
  enable_rest_api: false                 # Enable REST API
  api_host: "localhost"                  # API host
  api_port: 8080                         # API port
  
  # WebSocket settings
  enable_websocket: false                # Enable WebSocket
  websocket_port: 8081                   # WebSocket port
  
  # Authentication
  enable_authentication: false           # Enable API authentication
  api_key_required: false                # Require API key

# ═══════════════════════════════════════════════════════════════════════════════
# 🚀 GPU OPTIMIZATION CONFIGURATION
# ═══════════════════════════════════════════════════════════════════════════════
gpu:
  # GPU Settings
  enabled: true                              # Enable GPU acceleration
  memory_fraction: 0.8                       # Use 80% of GPU memory (0.1-0.9)
  device_id: 0                              # GPU device ID

  # Worker Configuration (TUNABLE)
  max_workers: 250                           # Maximum number of GPU workers (1-8)
  parallel_workers: 230                      # Active parallel workers (1-max_workers)
  worker_queue_size: 12                    # Task queue size per worker

  # Processing Settings (TUNABLE)
  batch_size: 5120                         # Batch size for GPU processing (256-4096)
  chunk_size: 100000                         # Data chunk size (10000-100000)
  parallel_streams: 80                       # Number of CUDA streams (1-8)
  max_concurrent_tasks: 500                  # Max concurrent GPU tasks (10-100)

  # Memory Management (TUNABLE)
  cleanup_frequency: 100                    # Clean GPU memory every N operations (50-500)
  enable_memory_pool: true                  # Enable memory pooling
  pool_size_gb: 6.0                        # Memory pool size (2.0-7.5 for RTX 3060Ti)
  memory_threshold: 0.85                    # Memory usage threshold for cleanup (0.7-0.95)

  # Performance Tuning (TUNABLE)
  optimization_trials: 300                   # Hyperparameter optimization trials (10-200)
  parallel_optimization_jobs: 100            # Parallel optimization jobs (1-8)
  strategy_batch_size: 150                   # Strategies processed per batch (5-50)

  # DYNAMIC PROCESSING LIMITS (NEW)
  stocks_per_worker: 3                       # Stocks per GPU worker (1-5)
  max_stocks_per_strategy: null              # Max stocks per strategy (null = no limit)
  min_stocks_per_strategy: 8                 # Minimum stocks per strategy (4-16)
  variants_per_stock: 5                      # Variants generated per stock (2-10)
  variants_per_result: 3                     # Variants per GPU result (1-5)

  # TIMEOUT CONFIGURATION (NEW)
  batch_timeout_seconds: 120                 # Base timeout for batch processing (60-300)
  timeout_per_combination: 3                 # Additional timeout per stock-timeframe combo (1-5)

  # GPU OPTIMIZATION SETTINGS (NEW)
  max_batch_size: 32                         # Maximum tasks per GPU batch (16-64)
  max_strategies_per_task: 8                 # Maximum strategies per GPU task (4-16)
  enable_memory_optimization: true           # Enable aggressive GPU memory management
  gpu_recovery_delay: 0.1                    # Minimal delay between batches (0.05-0.5)

  # PERFORMANCE OPTIMIZATIONS (NEW)
  enable_gpu_prewarming: true                # Pre-warm GPU on initialization
  enable_resource_preallocation: true        # Pre-allocate common GPU arrays
  enable_async_cleanup: true                 # Use asynchronous memory cleanup
  smart_memory_cleanup: true                 # Only cleanup when memory threshold exceeded

  # DYNAMIC PROCESSING LIMITS (NEW)
  stocks_per_worker: 3                       # Stocks per GPU worker (1-5)
  max_stocks_per_strategy: null              # Max stocks per strategy (null = no limit)
  min_stocks_per_strategy: 8                 # Minimum stocks per strategy (4-16)
  variants_per_stock: 5                      # Variants generated per stock (2-10)
  variants_per_result: 3                     # Variants per GPU result (1-5)

  # TIMEOUT CONFIGURATION (NEW)
  batch_timeout_seconds: 120                 # Base timeout for batch processing (60-300)
  timeout_per_combination: 3                 # Additional timeout per stock-timeframe combo (1-5)

  # Optimization Libraries
  libraries:
    pytorch: true                           # Enable PyTorch GPU
    cupy: true                             # Enable CuPy
    numba_cuda: true                       # Enable Numba CUDA
    polars_gpu: true                       # Enable Polars GPU engine

  # Fallback Settings
  cpu_fallback: true                        # Fall back to CPU if GPU fails
  fallback_threshold: 0.95                  # GPU memory threshold for fallback (0.8-0.99)

  # Performance Monitoring
  enable_monitoring: true                   # Enable GPU performance monitoring
  monitoring_interval: 1                   # Monitor every N seconds (5-60)
  log_performance_stats: true              # Log detailed performance statistics

# Evolved Strategies (managed by Strategy Evolution Agent)
evolved_strategies: []
