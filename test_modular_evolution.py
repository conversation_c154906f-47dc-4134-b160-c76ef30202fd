#!/usr/bin/env python3
"""
Test script for the modularized Enhanced Strategy Evolution Agent

This script tests that all the modular components work correctly together.
"""

import asyncio
import sys
from pathlib import Path

# Add the project root to the path
sys.path.append(str(Path(__file__).parent))

try:
    from agents.enhanced_strategy_evolution_agent import EnhancedStrategyEvolutionAgent
    print("✅ Successfully imported EnhancedStrategyEvolutionAgent")
except ImportError as e:
    print(f"❌ Failed to import EnhancedStrategyEvolutionAgent: {e}")
    sys.exit(1)

async def test_modular_components():
    """Test that all modular components are properly initialized"""
    try:
        print("🚀 Testing modular Enhanced Strategy Evolution Agent...")
        
        # Initialize the agent
        agent = EnhancedStrategyEvolutionAgent()
        print("✅ Agent initialized successfully")
        
        # Test that all modular components are available
        components = [
            ('genetic_algorithm', 'Genetic Algorithm'),
            ('variant_manager', 'Strategy Variant Manager'),
            ('yaml_manager', 'YAML Manager'),
            ('lifecycle_manager', 'Lifecycle Manager'),
            ('gpu_manager', 'GPU Processing Manager'),
            ('performance_tracker', 'Performance Tracker')
        ]
        
        for attr_name, component_name in components:
            if hasattr(agent, attr_name):
                component = getattr(agent, attr_name)
                if component is not None:
                    print(f"✅ {component_name} module loaded")
                else:
                    print(f"⚠️  {component_name} module is None")
            else:
                print(f"❌ {component_name} module not found")
        
        # Test basic functionality
        print("\n🧪 Testing basic functionality...")
        
        # Test performance tracker
        agent.performance_tracker.start_evolution_cycle()
        agent.performance_tracker.increment_stocks_tested(5)
        agent.performance_tracker.increment_strategies_processed(3)
        stats = agent.performance_tracker.get_evolution_stats()
        print(f"✅ Performance tracker working - Generation: {stats['generation_counter']}")
        
        # Test YAML manager
        config = agent.yaml_manager.get_default_config()
        if config and 'evolution' in config:
            print("✅ YAML manager working - Default config loaded")
        else:
            print("⚠️  YAML manager issue - Default config incomplete")
        
        # Test variant manager parameter space
        base_strategy = {'name': 'RSI_Reversal'}
        param_space = agent.variant_manager.get_parameter_space(base_strategy)
        if param_space and 'risk_reward_ratio' in param_space:
            print("✅ Variant manager working - Parameter space generated")
        else:
            print("⚠️  Variant manager issue - Parameter space incomplete")
        
        # Test fitness calculation
        test_metrics = {
            'sharpe_ratio': 1.5,
            'max_drawdown': 10.0,
            'win_rate': 0.6,
            'total_trades': 100,
            'total_pnl': 50.0,
            'roi': 15.0
        }
        composite_score = agent.gpu_manager.calculate_composite_fitness(test_metrics)
        if 0 <= composite_score <= 1:
            print(f"✅ GPU manager working - Composite fitness: {composite_score:.3f}")
        else:
            print(f"⚠️  GPU manager issue - Invalid composite score: {composite_score}")
        
        print("\n🎉 All modular components tested successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports():
    """Test that all modular components can be imported individually"""
    print("🔍 Testing individual module imports...")
    
    modules_to_test = [
        'agents.strategy_evolution.genetic_algorithm',
        'agents.strategy_evolution.strategy_variant_manager',
        'agents.strategy_evolution.yaml_manager',
        'agents.strategy_evolution.lifecycle_manager',
        'agents.strategy_evolution.gpu_processing_manager',
        'agents.strategy_evolution.performance_tracker'
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            return False
    
    return True

async def main():
    """Main test function"""
    print("="*60)
    print("🧬 MODULAR STRATEGY EVOLUTION AGENT TEST")
    print("="*60)
    
    # Test individual imports
    if not test_imports():
        print("❌ Module import tests failed")
        return
    
    print("\n" + "="*60)
    
    # Test integrated functionality
    success = await test_modular_components()
    
    print("\n" + "="*60)
    if success:
        print("🎉 ALL TESTS PASSED - Modularization successful!")
    else:
        print("❌ SOME TESTS FAILED - Check the errors above")
    print("="*60)

if __name__ == "__main__":
    asyncio.run(main())
