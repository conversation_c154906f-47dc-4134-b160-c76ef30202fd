22:48:15.616 📊 Processed 32/32 tasks successfully
[22:48:15.616] 🧬 Processing strategy 2/111: <PERSON><PERSON><PERSON>_Bounce_360ONE_1min
[22:48:15.616] 🎯 Using 120 stocks with 40 GPU workers (efficiency optimized)
[22:48:15.616] ⚡ TRUE parallel processing 120 stock-timeframe combinations
[22:48:15.616] 🚀 TRUE GPU Parallel optimization for <PERSON><PERSON><PERSON>_Bounce_360ONE_1min on 120 combinations
[22:48:15.616] ⚡ Using TRUE GPU parallel processing with 40 workers on 1 GPUs
22:48:19.095 📊 [  19.2s] CPU:  11.9% | RAM:  23.4% (7.3GB) | GPU0:   0.1% (0.0GB)
[22:48:28.980] 🔄 Large batch detected: 120 tasks, processing in chunks of 32
[22:48:28.980] 🔥 Processing batch 1: 32 tasks
22:48:28.980 🚀 Starting OPTIMIZED parallel batch processing of 32 tasks
22:48:28.980 📋 Batch queuing 32 tasks...
22:48:28.980 ⏱️ Using dynamic timeout: 190.0s for 32 tasks
22:48:28.980 ⚡ GPU Worker 32 processing task 360ONE_1min_0
22:48:28.981 ⚡ GPU Worker 35 processing task ABCAPITAL_1min_2
22:48:28.981 ⚡ GPU Worker 37 processing task ADANIENSOL_1min_4
22:48:28.981 ⚡ GPU Worker 38 processing task ADANIENT_1min_5